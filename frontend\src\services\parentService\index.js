import axios from "axios";
import config from "../../config";

export const parentService = {
  // API Service Methods
  parentRegisterService: (data) => {
    return axios.post(`${config.API_URL}/api/parents/parents`, data);
  },
  parentGetAllService: () => {
    return axios.get(`${config.API_URL}/api/parents/parents`);
  },
  parentGetByIdService: (parentId) => {
    return axios.get(`${config.API_URL}/api/parents/parents/${parentId}`);
  },
  parentGetByUserIdService: (userId) => {
    return axios.get(`${config.API_URL}/api/parents/parents/user/${userId}`);
  },
};

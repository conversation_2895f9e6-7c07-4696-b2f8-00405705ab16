import axios from "axios";
import config from "../../config";

export const authService = {
  // API Service Methods
  userLoginService: (data) => {
    return axios.post(`${config.LOGIN_URL}`, data);
  },
  sessionValidationService: (data) => {
    return axios.post(`${config.FORGET_URL}/validate_session`, data);
  },
  userRegisterService: (data) => {
    return axios.post(`${config.API_URL}/api/auth/register`, data);
  },
  userForgetService: (data) => {
    return axios.post(`${config.FORGET_URL}/forgot_password`, data);
  },
  userResetPasswordService: (data) => {
    return axios.post(`${config.FORGET_URL}/reset_password`, data);
  },
  userLogoutService: (data) => {
    return axios.post(`${config.API_URL}/api/auth/logout`, data);
  },
  userVerifyEmailService: (data) => {
    return axios.get(`${config.API_URL}/api/auth/verify_email`, data);
  },
  userVerifyOTPService: (data) => {
    return axios.post(`${config.API_URL}/api/auth/verify-otp`, data);
  },

  // Authentication Utility Methods
  /**
   * Login a user and store authentication data
   * @param {string} username - User's username
   * @param {string} password - User's password
   * @param {string} mainCode - Optional main code for school-wise separation
   * @returns {Promise} - Promise with login result
   */
  login: async (username, password, mainCode = null) => {
    try {
      const loginData = { username, password };
      if (mainCode) {
        loginData.main_code = mainCode;
      }

      const response = await authService.userLoginService(loginData);
      const { token, user } = response.data;

      // Store authentication data in session storage
      sessionStorage.setItem(config.AUTH.TOKEN_KEY, token);
      sessionStorage.setItem(config.AUTH.USER_KEY, JSON.stringify(user));

      // Store main_code if available (for Teachers and Admins)
      if (user.main_code) {
        sessionStorage.setItem(config.AUTH.MAIN_CODE_KEY, user.main_code);
      }

      return response.data;
    } catch (error) {
      throw error.response?.data || error;
    }
  },

  /**
   * Get current user from session storage
   * @returns {Object|null} - Current user object or null if not logged in
   */
  getCurrentUser: () => {
    try {
      const userStr = sessionStorage.getItem(config.AUTH.USER_KEY);
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Error parsing user data from session storage:', error);
      return null;
    }
  },

  /**
   * Get current authentication token
   * @returns {string|null} - Current token or null if not logged in
   */
  getToken: () => {
    return sessionStorage.getItem(config.AUTH.TOKEN_KEY);
  },

  /**
   * Get main code from session storage
   * @returns {string|null} - Main code or null if not available
   */
  getmainCode: () => {
    return sessionStorage.getItem(config.AUTH.MAIN_CODE_KEY);
  },

  /**
   * Verify the current token with the server
   * @returns {Promise} - Promise with verification result
   */
  verifyToken: async () => {
    try {
      const token = authService.getToken();
      if (!token) {
        return { valid: false, error: 'No token found' };
      }

      const response = await axios.post(`${config.AUTHENTICATION_URL}/api/auth/verify`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      return { valid: true, user: response.data.user };
    } catch (error) {
      return {
        valid: false,
        error: error.response?.data?.error || 'Token verification failed'
      };
    }
  },

  /**
   * Logout user and clear authentication data
   */
  logout: () => {
    // Clear all authentication data from session storage
    sessionStorage.removeItem(config.AUTH.TOKEN_KEY);
    sessionStorage.removeItem(config.AUTH.USER_KEY);
    sessionStorage.removeItem(config.AUTH.MAIN_CODE_KEY);
  },

  /**
   * Check if user is currently logged in
   * @returns {boolean} - True if user is logged in, false otherwise
   */
  isLoggedIn: () => {
    const token = authService.getToken();
    const user = authService.getCurrentUser();
    return !!(token && user);
  }
};

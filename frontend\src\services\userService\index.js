import axios from "axios";
import config from "../../config";

export const userService = {
  // API Service Methods
  userRegisterService: (data) => {
    return axios.post(`${config.API_URL}/api/users/register`, data);
  },
  userGetAllService: () => {
    return axios.get(`${config.API_URL}/api/users/users`);
  },
  userGetByIdService: (userId) => {
    return axios.get(`${config.API_URL}/api/users/users/${userId}`);
  },
};

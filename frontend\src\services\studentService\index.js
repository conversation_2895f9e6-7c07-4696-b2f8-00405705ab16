import axios from "axios";
import config from "../../config";

export const studentService = {
  // API Service Methods
  studentRegisterService: (data) => {
    return axios.post(`${config.API_URL}/api/students/students`, data);
  },
  studentGetAllService: () => {
    return axios.get(`${config.API_URL}/api/students/students`);
  },
  studentGetByIdService: (studentId) => {
    return axios.get(`${config.API_URL}/api/students/students/${studentId}`);
  },
  studentMapParentService: (data) => {
    return axios.post(`${config.API_URL}/api/students/map-parent`, data);
  },
  studentGetParentStudentsService: (parentId) => {
    return axios.get(`${config.API_URL}/api/students/parent-students/${parentId}`);
  },
  studentGetProfileService: () => {
    return axios.get(`${config.API_URL}/api/students/student-profile`);
  },
};

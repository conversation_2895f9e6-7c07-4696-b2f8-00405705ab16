import axios from "axios";
import config from "../../config";

export const courseService = {
  // API Service Methods
  courseCreateService: (data) => {
    return axios.post(`${config.API_URL}/api/courses/courses`, data);
  },
  courseGetAllService: () => {
    return axios.get(`${config.API_URL}/api/courses/courses`);
  },
  courseGetByIdService: (courseId) => {
    return axios.get(`${config.API_URL}/api/courses/courses/${courseId}`);
  },
  courseMapStudentService: (data) => {
    return axios.post(`${config.API_URL}/api/courses/map-student`, data);
  },
  courseGetStudentCoursesService: (studentId) => {
    return axios.get(`${config.API_URL}/api/courses/student-courses/${studentId}`);
  },
};
